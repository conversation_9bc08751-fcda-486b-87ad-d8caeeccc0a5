import { computed, ref, watch, onUnmounted } from 'vue'
import { useDict } from '/@/hooks/dict'
import { createChartWebSocket } from '/@/utils/websocket'
import { getRealTimeData } from '/@/api/dataview/structure'

// 全局共享的图表状态 - 简化版本
const sharedChartState = {
  // 共享的状态
  chartData: ref(null),
  baseData: ref(null),
  isLoading: ref(false),
  hasErrorOrNoData: ref(false),
  wsInstance: ref(null),
  legendSelected: ref({}),

  // 当前活跃的设备信息
  currentDevice: {
    deviceCode: null,
    structureType: null,
    structureId: null
  },

  // 引用计数
  refCount: 0,

  // 监听器数组
  watchers: [],

  // 清理方法
  cleanup() {
    if (this.wsInstance.value) {
      this.wsInstance.value.disconnect()
      this.wsInstance.value = null
    }
    this.watchers.forEach(unwatch => unwatch())
    this.watchers = []
    this.chartData.value = null
    this.baseData.value = null
    this.legendSelected.value = {}
  }
}

/**
 * 图表逻辑复用 composable - 使用共享状态
 */
export function useChart(props, options = {}) {
  const { chart_type } = useDict('chart_type')

  // 配置选项
  const config = {
    maxDataCount: 500,
    colors: ['#55EFF1', '#FFD700', '#FF6347', '#32CD32', '#6A5ACD', '#FF69B4'],
    ...options
  }

  // 增加引用计数
  sharedChartState.refCount++

  // 使用共享状态
  const {
    chartData,
    baseData,
    isLoading,
    hasErrorOrNoData,
    wsInstance,
    legendSelected
  } = sharedChartState

  // 根据数据名称获取图表类型
  const getChartType = (dataName) => {
    if (!chart_type.value || !Array.isArray(chart_type.value)) {
      return 'line'
    }
    const chartConfig = chart_type.value.find(item => item.label === dataName)
    if (chartConfig && chartConfig.value) {
      return chartConfig.value
    }
    return 'line'
  }

  // 时间格式化
  const formatTime = (timestamp) => {
    const date = new Date(timestamp)
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')

    // 支持响应式的dataTab
    const dataTab = (typeof props.dataTab === 'object' && props.dataTab.value) ? props.dataTab.value : (props.dataTab || '实时数据')
    if (dataTab === '实时数据') {
      return `${day} ${hours}:${minutes}:${seconds}`
    } else if (dataTab === '特征值数据') {
      return `${month}-${day} ${hours}:${minutes}`
    }
    return `${hours}:${minutes}`
  }

  // 数据合并 - 追加模式
  const mergeData = (existingData, newData) => {
    if (!existingData) {
      return newData
    }

    if (!newData || !newData.eventTime || newData.eventTime.length === 0) {
      return existingData
    }

    const merged = { ...existingData }

    if (newData.eventTime && existingData.eventTime) {
      merged.eventTime = [...existingData.eventTime, ...newData.eventTime]

      Object.keys(newData).forEach(key => {
        if (key !== 'eventTime' && Array.isArray(newData[key])) {
          if (merged[key] && Array.isArray(merged[key])) {
            merged[key] = [...merged[key], ...newData[key]]
          } else {
            merged[key] = [...newData[key]]
          }
        }
      })

      // 按时间排序
      const timeIndexPairs = merged.eventTime.map((time, index) => ({ time, index }))
      timeIndexPairs.sort((a, b) => new Date(a.time) - new Date(b.time))

      merged.eventTime = timeIndexPairs.map(pair => pair.time)
      Object.keys(merged).forEach(key => {
        if (key !== 'eventTime' && Array.isArray(merged[key])) {
          const originalArray = [...merged[key]]
          merged[key] = timeIndexPairs.map(pair => originalArray[pair.index])
        }
      })

      // 限制数据长度
      if (merged.eventTime.length > config.maxDataCount) {
        const removeCount = merged.eventTime.length - config.maxDataCount
        merged.eventTime = merged.eventTime.slice(removeCount)
        Object.keys(merged).forEach(key => {
          if (key !== 'eventTime' && Array.isArray(merged[key])) {
            merged[key] = merged[key].slice(removeCount)
          }
        })
      }
    } else {
      Object.assign(merged, newData)
    }

    return merged
  }

  // 获取基础数据
  const fetchBaseData = async () => {
    try {
      isLoading.value = true
      hasErrorOrNoData.value = false

      // 支持响应式的dataTab
      const dataTab = (typeof props.dataTab === 'object' && props.dataTab.value) ? props.dataTab.value : (props.dataTab || '实时数据')
      const isRealTime = dataTab === '实时数据' ? 0 : 1
      const res = await getRealTimeData(props.deviceCode, props.structureType, isRealTime)

      if (res.data && res.data.eventTime && res.data.eventTime.length > 0) {
        baseData.value = res.data
        chartData.value = res.data
        hasErrorOrNoData.value = false
      } else {
        baseData.value = null
        chartData.value = null
        hasErrorOrNoData.value = true
      }
    } catch (error) {
      console.error('Failed to fetch base data:', error)
      baseData.value = null
      chartData.value = null
      hasErrorOrNoData.value = true
    } finally {
      isLoading.value = false
    }
  }

  // 初始化WebSocket连接 - 只为实时数据创建
  const initWebSocket = (deviceCode) => {
    // 检查当前数据类型，只为实时数据创建WebSocket连接
    const dataTab = (typeof props.dataTab === 'object' && props.dataTab.value) ? props.dataTab.value : (props.dataTab || '实时数据')
    if (dataTab !== '实时数据') {
      // 特征值数据不需要WebSocket连接
      return
    }

    if (wsInstance.value) {
      wsInstance.value.disconnect()
    }

    // 创建WebSocket连接，并在连接建立后发送设备code
    wsInstance.value = createChartWebSocket((data) => {
      const hasValidData = data && data.eventTime && data.eventTime.length > 0

      if (hasValidData) {
        if (chartData.value) {
          chartData.value = mergeData(chartData.value, data)
        } else if (baseData.value) {
          chartData.value = mergeData(baseData.value, data)
        } else {
          chartData.value = data
        }
        hasErrorOrNoData.value = false
      } else {
        if (!chartData.value && baseData.value) {
          chartData.value = baseData.value
        }
        if (!chartData.value && !baseData.value) {
          hasErrorOrNoData.value = true
        }
      }
    })

    // 连接WebSocket
    wsInstance.value.connect()

    // 在连接建立后发送设备code
    if (deviceCode) {
      // 使用更长的延时确保连接建立，并添加重试机制
      const sendCodeWithRetry = (code, retryCount = 0) => {
        setTimeout(() => {
          if (wsInstance.value && wsInstance.value.isConnected() && code) {
            wsInstance.value.send(code)
          } else if (retryCount < 5) {
            // 如果连接还没建立，重试最多5次
            sendCodeWithRetry(code, retryCount + 1)
          }
        }, 200 + retryCount * 100) // 递增延时：200ms, 300ms, 400ms...
      }

      sendCodeWithRetry(deviceCode)
    }
  }

  // 发送设备代码到WebSocket
  const sendDeviceCode = (code) => {
    if (wsInstance.value && wsInstance.value.isConnected() && code) {
      wsInstance.value.send(code)
    }
  }

  // 处理图例点击事件
  const handleLegendSelectChanged = (params) => {
    legendSelected.value = { ...params.selected }
  }

  // 图表配置选项
  const option = computed(() => {
    const seriesData = chartData.value
      ? Object.keys(chartData.value)
          .filter(key => key !== 'eventTime')
          .map((key, index) => ({
            name: key,
            data: chartData.value[key],
            color: config.colors[index % config.colors.length],
          }))
      : []
    const xData = chartData.value?.eventTime?.map(formatTime) || []

    return {
      grid: { left: '2%', right: '2%', top: '15%', bottom: '5%', containLabel: true },
      tooltip: { show: true, trigger: 'axis' },
      legend: {
        show: true,
        right: '10%',
        top: '0%',
        textStyle: { color: '#fff', fontSize: 12 },
        selected: legendSelected.value,
        type: 'scroll',
        orient: 'horizontal',
        pageButtonItemGap: 5,
        pageButtonGap: 30,
        pageButtonPosition: 'end',
        pageFormatter: '{current}/{total}',
        pageIconColor: '#aaa',
        pageIconInactiveColor: '#2f4554',
        pageIconSize: 15,
        pageTextStyle: { color: '#fff', fontSize: 10 },
        height: 60,
      },
      xAxis: [{
        type: 'category',
        axisLine: { show: true, lineStyle: { color: '#85C1D9' } },
        axisTick: { show: false },
        axisLabel: { color: '#8BC4F2', margin: 6 },
        splitLine: { show: false },
        boundaryGap: ['5%', '5%'],
        data: xData,
      }],
      yAxis: [{
        type: 'value',
        nameTextStyle: { color: '#8BC4F2', padding: [0, 0, 0, 30] },
        min: (value) => value.min,
        max: (value) => value.max,
        axisLabel: { color: '#8BC4F2', margin: 6 },
        splitLine: { lineStyle: { color: '#355C84', type: 'dashed' } },
      }],
      series: seriesData.map(s => ({
        name: s.name,
        type: getChartType(s.name),
        symbolSize: 6,
        itemStyle: { color: s.color, borderColor: s.color, borderWidth: 2 },
        lineStyle: { color: s.color },
        connectNulls: true,
        data: s.data || [],
      })),
    }
  })

  // 监听props变化
  const setupWatchers = () => {
    debugger
    // 只在第一次引用时设置监听器
    if (sharedChartState.watchers.length === 0) {
      // 监听图例选择状态变化
      const unwatch0 = watch(() => chartData.value, (newData, oldData) => {
        if (newData) {
          const newKeys = Object.keys(newData).filter(key => key !== 'eventTime')
          const oldKeys = oldData ? Object.keys(oldData).filter(key => key !== 'eventTime') : []

          const seriesChanged = newKeys.length !== oldKeys.length ||
            newKeys.some(key => !oldKeys.includes(key)) ||
            oldKeys.some(key => !newKeys.includes(key))

          if (seriesChanged || Object.keys(legendSelected.value).length === 0) {
            const newLegendSelected = {}
            newKeys.forEach((key, index) => {
              if (legendSelected.value.hasOwnProperty(key)) {
                newLegendSelected[key] = legendSelected.value[key]
              } else {
                newLegendSelected[key] = index === 0
              }
            })
            legendSelected.value = newLegendSelected
          }
        }
      }, { immediate: true })
      const unwatch1 = watch([
        () => props.deviceCode,
        () => props.dataTab,
        () => props.structureId
      ], async ([newCode, newDataTab, newStructureId], [, , oldStructureId]) => {
        if (oldStructureId !== undefined && newStructureId !== oldStructureId) {
          chartData.value = null
          baseData.value = null
          hasErrorOrNoData.value = false
          if (!newCode) return
        }

        if (!props.deviceCode) return
        if (!newCode) {
          chartData.value = null
          baseData.value = null
          hasErrorOrNoData.value = false
          return
        }

        await fetchBaseData()

        // 支持响应式的dataTab
        const dataTab = (typeof newDataTab === 'object' && newDataTab.value) ? newDataTab.value : (newDataTab || '实时数据')
        if (dataTab === '实时数据') {
          // 实时数据需要WebSocket连接
          if (!wsInstance.value) {
            // 创建WebSocket连接并在连接建立后发送设备code
            initWebSocket(newCode)
          } else {
            // 如果WebSocket已存在，直接发送设备code
            sendDeviceCode(newCode)
          }
        } else {
          // 特征值数据不需要WebSocket连接，如果存在则断开
          if (wsInstance.value) {
            wsInstance.value.disconnect()
            wsInstance.value = null
          }
        }
      }, { immediate: true })

      // 存储监听器以便清理
      sharedChartState.watchers.push(unwatch0, unwatch1)
    }
  }

  // 清理资源
  onUnmounted(() => {
    // 减少引用计数
    sharedChartState.refCount--

    // 如果没有其他组件引用，清理共享状态
    if (sharedChartState.refCount <= 0) {
      sharedChartState.cleanup()
    }
  })

  return {
    // 状态
    chartData,
    baseData,
    isLoading,
    hasErrorOrNoData,
    legendSelected,
    wsInstance,
    
    // 计算属性
    option,
    
    // 方法
    handleLegendSelectChanged,
    fetchBaseData,
    initWebSocket,
    sendDeviceCode,
    setupWatchers,
    
    // 工具函数
    getChartType,
    formatTime,
    mergeData
  }
}
